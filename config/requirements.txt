# Core FastAPI dependencies
fastapi>=0.100.0
uvicorn[standard]>=0.23.0
pydantic[email]>=2.0.0
pydantic-settings>=2.0.0

# Database
sqlalchemy>=2.0.0
alembic>=1.12.0
asyncpg>=0.28.0
psycopg2-binary>=2.9.0
greenlet>=2.0.0

# Cache and messaging
redis>=5.0.0
celery>=5.3.0

# Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
cryptography>=40.0.0

# HTTP and file handling
httpx>=0.24.0
python-multipart>=0.0.6
aiofiles>=23.0.0

# Templating and validation
jinja2>=3.1.0
openapi-spec-validator>=0.6.0
graphql-core>=3.2.0

# Container orchestration
kubernetes>=28.0.0
docker>=6.1.0

# Monitoring and logging
prometheus-client>=0.18.0
structlog>=23.0.0

# MCP SDK
mcp>=1.0.0

# Development dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0
factory-boy==3.3.0
faker==20.1.0

# Testing with containers
testcontainers==3.7.1
pytest-postgresql==5.0.0
pytest-redis==3.0.2

# Task monitoring
flower==2.0.1
