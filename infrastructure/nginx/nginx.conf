# JomMCP Platform - Main Nginx Configuration
# Optimized for performance, security, and scalability

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Worker connections and file limits
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Buffer sizes
    client_body_buffer_size 128k;
    client_max_body_size 100m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # Timeouts
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/x-javascript
        application/xml+rss
        application/javascript
        application/json
        application/xml
        application/rss+xml
        application/atom+xml
        image/svg+xml;
    
    # Security headers (global)
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    
    # Upstream definitions
    upstream api_gateway {
        server api-gateway:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream web_ui {
        server web-ui:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream registration_service {
        server registration-service:8081 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }
    
    upstream generator_service {
        server generator-service:8082 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }
    
    upstream deployment_service {
        server deployment-service:8083 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }
    
    upstream docs_service {
        server docs-service:8084 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }
    
    upstream prometheus {
        server prometheus:9090 max_fails=3 fail_timeout=30s;
        keepalive 8;
    }
    
    upstream grafana {
        server grafana:3000 max_fails=3 fail_timeout=30s;
        keepalive 8;
    }
    
    # Include server configurations
    include /etc/nginx/conf.d/*.conf;
}
