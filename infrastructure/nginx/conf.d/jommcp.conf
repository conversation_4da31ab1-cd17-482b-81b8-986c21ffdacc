# JomMCP Platform - Domain Configuration Template
# This file is a template for custom domain configuration
# Use scripts/setup-domain.sh to generate the actual configuration

# HTTP to HTTPS redirect (when SSL is enabled)
server {
    listen 80;
    server_name DOMAIN_NAME_PLACEHOLDER www.DOMAIN_NAME_PLACEHOLDER;
    
    # Health check endpoint (always available on HTTP)
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Let's Encrypt challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    # Redirect all other traffic to HTTPS (when SSL is enabled)
    location / {
        # This will be dynamically configured based on ENABLE_SSL
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS server (when SSL is enabled)
server {
    listen 443 ssl http2;
    server_name DOMAIN_NAME_PLACEHOLDER www.DOMAIN_NAME_PLACEHOLDER;
    
    # SSL configuration
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # Include SSL security settings
    include /etc/nginx/snippets/ssl-params.conf;
    include /etc/nginx/snippets/security-headers.conf;
    
    # Rate limiting
    limit_req zone=general burst=50 nodelay;
    limit_conn conn_limit_per_ip 20;
    
    # Root location - Web UI
    location / {
        proxy_pass http://web_ui;
        include /etc/nginx/snippets/proxy-params.conf;
        
        # Special handling for Next.js
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
    }
    
    # API Gateway routes
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://api_gateway;
        include /etc/nginx/snippets/proxy-params.conf;
    }
    
    # WebSocket support for real-time features
    location /ws/ {
        proxy_pass http://api_gateway;
        include /etc/nginx/snippets/websocket-params.conf;
    }
    
    # Direct service access (for development/debugging)
    location /services/registration/ {
        limit_req zone=api burst=10 nodelay;
        proxy_pass http://registration_service/;
        include /etc/nginx/snippets/proxy-params.conf;
    }
    
    location /services/generator/ {
        limit_req zone=api burst=10 nodelay;
        proxy_pass http://generator_service/;
        include /etc/nginx/snippets/proxy-params.conf;
    }
    
    location /services/deployment/ {
        limit_req zone=api burst=10 nodelay;
        proxy_pass http://deployment_service/;
        include /etc/nginx/snippets/proxy-params.conf;
    }
    
    location /services/docs/ {
        limit_req zone=api burst=10 nodelay;
        proxy_pass http://docs_service/;
        include /etc/nginx/snippets/proxy-params.conf;
    }
    
    # Monitoring endpoints
    location /monitoring/prometheus/ {
        auth_basic "Monitoring Access";
        auth_basic_user_file /etc/nginx/.htpasswd;
        proxy_pass http://prometheus/;
        include /etc/nginx/snippets/proxy-params.conf;
    }
    
    location /monitoring/grafana/ {
        proxy_pass http://grafana/;
        include /etc/nginx/snippets/proxy-params.conf;
    }
    
    # Static assets optimization
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
        
        # Try to serve from web-ui first, then fallback to API gateway
        try_files $uri @web_ui_static;
    }
    
    location @web_ui_static {
        proxy_pass http://web_ui;
        include /etc/nginx/snippets/proxy-params.conf;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security.txt
    location /.well-known/security.txt {
        return 200 "Contact: <EMAIL>\nExpires: 2025-12-31T23:59:59.000Z\n";
        add_header Content-Type text/plain;
    }
    
    # Robots.txt
    location /robots.txt {
        return 200 "User-agent: *\nDisallow: /api/\nDisallow: /services/\nDisallow: /monitoring/\n";
        add_header Content-Type text/plain;
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|log|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
