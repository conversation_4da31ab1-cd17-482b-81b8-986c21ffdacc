# JomMCP Platform - Localhost Development Configuration
# This configuration is used when DOMAIN_NAME is localhost or not set

server {
    listen 80 default_server;
    server_name localhost 127.0.0.1;
    
    # Security headers for development
    include /etc/nginx/snippets/security-headers.conf;
    
    # Rate limiting (more relaxed for development)
    limit_req zone=general burst=100 nodelay;
    limit_conn conn_limit_per_ip 50;
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "JomMCP Platform - Healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Root location - Web UI
    location / {
        proxy_pass http://web_ui;
        include /etc/nginx/snippets/proxy-params.conf;
        
        # Development-specific headers
        add_header X-Development-Mode "true";
    }
    
    # API Gateway routes
    location /api/ {
        limit_req zone=api burst=50 nodelay;
        proxy_pass http://api_gateway;
        include /etc/nginx/snippets/proxy-params.conf;
    }
    
    # WebSocket support
    location /ws/ {
        proxy_pass http://api_gateway;
        include /etc/nginx/snippets/websocket-params.conf;
    }
    
    # Direct service access for development
    location /services/registration/ {
        proxy_pass http://registration_service/;
        include /etc/nginx/snippets/proxy-params.conf;
        add_header X-Service "registration" always;
    }
    
    location /services/generator/ {
        proxy_pass http://generator_service/;
        include /etc/nginx/snippets/proxy-params.conf;
        add_header X-Service "generator" always;
    }
    
    location /services/deployment/ {
        proxy_pass http://deployment_service/;
        include /etc/nginx/snippets/proxy-params.conf;
        add_header X-Service "deployment" always;
    }
    
    location /services/docs/ {
        proxy_pass http://docs_service/;
        include /etc/nginx/snippets/proxy-params.conf;
        add_header X-Service "docs" always;
    }
    
    # Monitoring endpoints (no auth for development)
    location /monitoring/prometheus/ {
        proxy_pass http://prometheus/;
        include /etc/nginx/snippets/proxy-params.conf;
        add_header X-Monitoring "prometheus" always;
    }
    
    location /monitoring/grafana/ {
        proxy_pass http://grafana/;
        include /etc/nginx/snippets/proxy-params.conf;
        add_header X-Monitoring "grafana" always;
    }
    
    # Development tools and debugging
    location /debug/ {
        return 200 "Debug endpoint - JomMCP Platform\nTimestamp: $time_iso8601\nRequest ID: $request_id\n";
        add_header Content-Type text/plain;
        add_header X-Debug-Info "enabled";
    }
    
    # Nginx status for monitoring
    location /nginx-status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        allow **********/12;  # Docker networks
        deny all;
    }
    
    # Static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1h;  # Shorter cache for development
        add_header Cache-Control "public";
        
        try_files $uri @web_ui_static;
    }
    
    location @web_ui_static {
        proxy_pass http://web_ui;
        include /etc/nginx/snippets/proxy-params.conf;
    }
    
    # Development-friendly error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        return 404 "JomMCP Platform - Page Not Found\nDevelopment Mode\n";
        add_header Content-Type text/plain;
    }
    
    location = /50x.html {
        return 500 "JomMCP Platform - Server Error\nDevelopment Mode\nCheck service logs for details\n";
        add_header Content-Type text/plain;
    }
}
