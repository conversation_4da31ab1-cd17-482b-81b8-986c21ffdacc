# Common proxy parameters for JomMCP Platform
# Include this file in proxy_pass locations

# Basic proxy headers
proxy_set_header Host $host;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_set_header X-Forwarded-Host $host;
proxy_set_header X-Forwarded-Port $server_port;

# Request ID for tracing
proxy_set_header X-Request-ID $request_id;

# Connection settings
proxy_connect_timeout 30s;
proxy_send_timeout 60s;
proxy_read_timeout 60s;

# Buffer settings
proxy_buffering on;
proxy_buffer_size 4k;
proxy_buffers 8 4k;
proxy_busy_buffers_size 8k;

# HTTP version and connection handling
proxy_http_version 1.1;
proxy_set_header Connection "";

# Disable proxy cache by default (can be overridden)
proxy_cache_bypass 1;
proxy_no_cache 1;

# Error handling
proxy_intercept_errors off;
proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
proxy_next_upstream_tries 3;
proxy_next_upstream_timeout 30s;
