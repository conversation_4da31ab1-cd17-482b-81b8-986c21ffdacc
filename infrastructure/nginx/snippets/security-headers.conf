# Security headers for JomMCP Platform
# Include this file to add security headers to responses

# Prevent clickjacking
add_header X-Frame-Options "SAMEORIGIN" always;

# XSS protection
add_header X-XSS-Protection "1; mode=block" always;

# Prevent MIME type sniffing
add_header X-Content-Type-Options "nosniff" always;

# Referrer policy
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# Content Security Policy (relaxed for development)
add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: http: https:; img-src 'self' data: https:; font-src 'self' data: https:;" always;

# Permissions policy
add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), accelerometer=(), gyroscope=()" always;

# HSTS (only for HTTPS)
# add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# Remove server information
more_clear_headers Server;
server_tokens off;
