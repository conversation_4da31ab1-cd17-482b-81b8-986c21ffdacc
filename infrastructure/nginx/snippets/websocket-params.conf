# WebSocket proxy parameters for JomMCP Platform
# Include this file for WebSocket proxy locations

# Include basic proxy parameters
include /etc/nginx/snippets/proxy-params.conf;

# WebSocket specific headers
proxy_set_header Upgrade $http_upgrade;
proxy_set_header Connection "upgrade";

# WebSocket timeouts (longer than regular HTTP)
proxy_connect_timeout 60s;
proxy_send_timeout 300s;
proxy_read_timeout 300s;

# Disable buffering for WebSocket
proxy_buffering off;

# Keep connection alive
proxy_set_header Connection "keep-alive";
proxy_http_version 1.1;

# WebSocket specific settings
proxy_cache off;
proxy_redirect off;
