# Development Dockerfile for JomMCP Web UI
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dependencies
COPY ./apps/web-ui/package.json ./apps/web-ui/package-lock.json* ./
RUN npm install

# Copy source code
COPY ./apps/web-ui .

# Expose port
EXPOSE 3000

# Set environment variables
ENV NODE_ENV development
ENV NEXT_TELEMETRY_DISABLED 1

# Start development server
CMD ["npm", "run", "dev"]
